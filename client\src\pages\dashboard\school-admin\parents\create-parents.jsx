import React, { useState } from "react";
import { Container } from "@/components/ui/container";
import { ParentForm } from "@/components/forms/dashboard/parents/parent-form";
import { PageHeader } from "@/components/dashboard/page-header";
import { useNavigate, useParams } from "react-router-dom";
import { FormCardSkeleton } from "@/components/forms/form-card-skeleton";

const CreateParents = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(!!id);

  return (
    <Container className="py-8">
      <PageHeader
        title={id ? "Edit Parent" : "Create New Parent"}
        actions={[
          {
            label: "Back to Parents",
            href: "/dashboard/parents",
          },
        ]}
        breadcrumbs={[
          { label: "Dashboard", href: "/dashboard" },
          { label: "Parents", href: "/dashboard/parents" },
          { label: id ? "Edit Parent" : "Create Parent" },
        ]}
      />

      {loading ? (
        <FormCardSkeleton />
      ) : (
        <ParentForm editingId={id} initialData={[]} />
      )}
    </Container>
  );
};

export default CreateParents;
