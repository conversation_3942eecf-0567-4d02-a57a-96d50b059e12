import React from "react";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Switch } from "@/components/ui/switch";

export function SwitchInput({
  form,
  name,
  label,
  icon,
  description,
  validation = {},
  switchProps = {},
}) {
  return (
    <FormField
      control={form.control}
      name={name}
      rules={validation}
      render={({ field }) => (
        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
          <FormControl>
            <Switch
              checked={field.value || false}
              onCheckedChange={field.onChange}
              {...switchProps}
            />
          </FormControl>
          <div className="space-y-1 leading-none">
            <FormLabel className="flex items-center gap-2">
              {icon && <span className="h-4 w-4">{icon}</span>}
              {label}
            </FormLabel>
            {description && <FormDescription>{description}</FormDescription>}
          </div>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export default SwitchInput;
